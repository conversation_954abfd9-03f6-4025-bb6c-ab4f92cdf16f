{"version": 2, "data": {"api::api-access-key.api-access-key": {}, "api::plan.plan": {}, "api::monthly-usage.monthly-usage": {}, "api::subscription-order.subscription-order": {}, "plugin::users-permissions.user": {}, "api::organization.organization": {}, "api::session.session": {}, "api::contact-booking.contact-booking": {}, "api::scrumboard-list.scrumboard-list": {}, "api::scrumboard.scrumboard": {}, "api::scrumboard-comment.scrumboard-comment": {}, "api::label.label": {}, "api::scrumboard-card.scrumboard-card": {}, "api::scrumboard-automation.scrumboard-automation": {}, "api::knowledgebase.knowledgebase": {}, "plugin::users-permissions.permission": {}, "plugin::users-permissions.role": {}, "api::answer.answer": {}, "api::browser-info.browser-info": {}, "api::customer-info.customer-info": {}, "api::machine-info.machine-info": {}, "api::inbuilt-tool.inbuilt-tool": {}, "api::api-tool.api-tool": {}, "api::email-integration.email-integration": {}, "api::ai-agent.ai-agent": {}, "api::chatbot-style.chatbot-style": {}, "api::shopify-model.shopify-model": {}, "api::file-results-model.file-results-model": {}, "api::zendesk-integration-model.zendesk-integration-model": {}, "api::slack-integration-model.slack-integration-model": {}, "api::whatsapp-integration-model.whatsapp-integration-model": {}, "api::datasource.datasource": {}, "api::ai-task.ai-task": {}, "api::train.train": {}, "api::file-train.file-train": {}, "api::email-template.email-template": {"21": {"id": 21, "name": "confirmation-email", "display_name": "Production-ready email confirmation template with inline CSS and embedded logo", "description": "Production-ready email confirmation template with inline CSS and embedded logo", "provider": "postmark", "template_id": "", "html_content": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n  <title>Confirm Your Email</title>\n</head>\n<body style=\"margin: 0; padding: 0; background-color: #F9FAFB; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; color: #374151;\">\n\n  <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"border-collapse: collapse;\">\n    <tr>\n      <td align=\"center\" style=\"padding: 40px 20px;\">\n        <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\">\n          \n          <!-- Top-left logo -->\n          <tr>\n            <td style=\"padding: 20px 30px 0;\">\n              <img src=\"https://app-dev.ajentic.com/assets/images/logo/ajentic-logo.png\" alt=\"Ajentic\" style=\"height: 36px; width: auto;\" />\n            </td>\n          </tr>\n\n          <!-- Centered image -->\n          <tr>\n            <td align=\"center\" style=\"padding: 20px 30px 0;\">\n              <img src=\"https://talkbase-chatbot-images.s3.ap-south-1.amazonaws.com/emailers/confirm-email-ajentic.png\" alt=\"Confirm Email\" style=\"max-width: 180px; height: auto; display: block; margin: 0 auto;\" />\n            </td>\n          </tr>\n\n          <!-- Content -->\n          <tr>\n            <td style=\"padding: 30px 40px; text-align: center;\">\n              <h1 style=\"font-size: 22px; font-weight: 700; color: #111827; margin-bottom: 16px;\">Confirm Your Email</h1>\n              <p style=\"font-size: 15px; color: #4B5563; margin-bottom: 30px;\">Hi {{name}}, please confirm your email address to activate your account and start using your AI assistant.</p>\n\n              <!-- CTA button -->\n              <a href=\"{{confirmation_url}}\" style=\"background: #8B5CF6; color: #ffffff; padding: 12px 28px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;\">Confirm Email</a>\n\n              <!-- Fallback link -->\n              <p style=\"font-size: 13px; color: #9CA3AF; margin-top: 30px;\">If the button above doesn’t work, copy and paste this link into your browser:</p>\n              <p style=\"font-size: 13px; word-break: break-all; color: #6B7280;\">\n                <a href=\"{{confirmation_url}}\" style=\"color: #6B7280; text-decoration: underline;\">{{confirmation_url}}</a>\n              </p>\n\n              <!-- Disclaimer -->\n              <p style=\"font-size: 12px; color: #9CA3AF; margin-top: 20px;\">If you didn’t request this, you can safely ignore this email. Your password will remain unchanged.</p>\n            </td>\n          </tr>\n\n        </table>\n      </td>\n    </tr>\n  </table>\n\n</body>\n</html>\n", "text_content": null, "template_type": "html_content", "category": "verification", "subject": "Confirm your email to get started!", "default_from_email": "<EMAIL>", "default_from_name": "Ajentic Team", "variables": {"name": "{{user.name}}", "email": "{{user.email}}", "user_id": "{{user.id}}", "company_name": "{{organization.name}}", "dashboard_url": "{{app.dashboardUrl}}", "support_email": "{{organization.supportEmail}}", "organization_id": "{{organization.id}}", "confirmation_url": "{{auth.confirmationUrl}}"}, "required_variables": [], "is_active": true, "scope": "global", "usage_count": 5, "last_used": "2025-08-05T04:42:54.619Z", "tags": [], "version": "2.0.0", "preview_data": {}, "css_styles": null, "responsive_design": true, "analytics_enabled": true, "createdAt": "2025-08-01T05:16:02.723Z", "updatedAt": "2025-08-05T04:45:13.577Z", "publishedAt": "2025-08-05T03:36:15.757Z", "parent_template": null, "created_by_user": null, "organization": null, "createdBy": null, "updatedBy": null}, "22": {"id": 22, "name": "reset-password-email", "display_name": "Production-ready password reset email with inline CSS and security focus", "description": "Production-ready password reset email with inline CSS and security focus", "provider": "postmark", "template_id": "", "html_content": "<!DOCTYPE html>\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\n<head>\n  <meta charset=\"UTF-8\" />\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n  <title>Reset Your Password</title>\n</head>\n<body style=\"margin: 0; padding: 0; background-color: #F3F4F6; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; color: #374151;\">\n\n  <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n    <tr>\n      <td align=\"center\" style=\"padding: 40px 20px;\">\n        <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\">\n          \n          <!-- <PERSON><PERSON> (top-left) -->\n          <tr>\n            <td style=\"padding: 20px 30px 0;\">\n              <img src=\"https://app-dev.ajentic.com/assets/images/logo/ajentic-logo.png\" alt=\"{{company_name}}\" style=\"height: 36px; width: auto;\" />\n            </td>\n          </tr>\n\n          <!-- Centered Image -->\n          <tr>\n            <td align=\"center\" style=\"padding: 20px 30px 0;\">\n              <img src=\"https://talkbase-chatbot-images.s3.ap-south-1.amazonaws.com/emailers/reset-password-ajenti.png\" alt=\"Reset Password\" style=\"max-width: 180px; height: auto; display: block; margin: 0 auto;\" />\n            </td>\n          </tr>\n\n          <!-- Content -->\n          <tr>\n            <td align=\"center\" style=\"padding: 30px 40px;\">\n              <h1 style=\"font-size: 22px; font-weight: 700; color: #111827; margin-bottom: 16px;\">Reset Your Password</h1>\n              <p style=\"font-size: 15px; color: #4B5563; margin-bottom: 30px;\">Hi {{name}}, we received a request to reset your password. Click the button below to choose a new password. This link is valid for a limited time for your security.</p>\n\n              <!-- Button -->\n              <a href=\"{{reset_url}}\" style=\"background: #8B5CF6; color: #ffffff; padding: 12px 28px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;\">Reset Password</a>\n\n              <!-- Fallback URL -->\n              <p style=\"font-size: 13px; color: #9CA3AF; margin-top: 30px;\">If the button above doesn’t work, copy and paste this link into your browser:</p>\n              <p style=\"font-size: 13px; word-break: break-all; color: #6B7280;\">\n                <a href=\"{{reset_url}}\" style=\"color: #6B7280; text-decoration: underline;\">{{reset_url}}</a>\n              </p>\n\n              <!-- Disclaimer -->\n              <p style=\"font-size: 12px; color: #9CA3AF; margin-top: 20px;\">If you didn’t request this, you can safely ignore this email. Your password will remain unchanged.</p>\n            </td>\n          </tr>\n\n        </table>\n      </td>\n    </tr>\n  </table>\n\n</body>\n</html>\n", "text_content": null, "template_type": "html_content", "category": "reset-password", "subject": "Forgot your password? Let's fix that.", "default_from_email": "<EMAIL>", "default_from_name": "Ajentic Security Team", "variables": {"name": "{{user.name}}", "email": "{{user.email}}", "user_id": "{{user.id}}", "reset_url": "{{auth.resetPasswordUrl}}", "company_name": "{{organization.name}}", "support_email": "{{organization.supportEmail}}", "security_email": "{{organization.securityEmail}}", "organization_id": "{{organization.id}}"}, "required_variables": [], "is_active": true, "scope": "global", "usage_count": 1, "last_used": "2025-08-05T03:42:54.997Z", "tags": [], "version": "2.0.0", "preview_data": {}, "css_styles": null, "responsive_design": true, "analytics_enabled": true, "createdAt": "2025-08-01T05:16:02.789Z", "updatedAt": "2025-08-05T04:40:30.593Z", "publishedAt": "2025-08-05T03:39:18.653Z", "parent_template": null, "created_by_user": null, "organization": null, "createdBy": null, "updatedBy": null}, "23": {"id": 23, "name": "ticket-notification", "display_name": "Production-ready ticket notification email with inline CSS and comprehensive ticket details", "description": "Production-ready ticket notification email with inline CSS and comprehensive ticket details", "provider": "postmark", "template_id": "", "html_content": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n  <title>New Support Ticket</title>\n</head>\n<body style=\"margin: 0; padding: 0; background-color: #F3F4F6; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; color: #374151;\">\n\n  <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n    <tr>\n      <td align=\"center\" style=\"padding: 40px 20px;\">\n        <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\">\n          \n          <!-- Logo -->\n          <tr>\n            <td style=\"padding: 20px 30px 0;\">\n              <img src=\"https://app-dev.ajentic.com/assets/images/logo/ajentic-logo.png\" alt=\"{{company_name}}\" style=\"height: 36px; width: auto;\" />\n            </td>\n          </tr>\n\n          <!-- Header Image -->\n          <tr>\n            <td align=\"center\" style=\"padding: 20px 30px 0;\">\n              <img src=\"https://talkbase-chatbot-images.s3.ap-south-1.amazonaws.com/emailers/ticket-create-ajentic.png\" alt=\"New Ticket\" style=\"max-width: 180px; height: auto; display: block; margin: 0 auto;\" />\n            </td>\n          </tr>\n\n          <!-- Content -->\n          <tr>\n            <td align=\"center\" style=\"padding: 30px 40px;\">\n              <h1 style=\"font-size: 22px; font-weight: 700; color: #111827; margin-bottom: 8px;\">New Support Ticket</h1>\n              <p style=\"font-size: 15px; color: #4B5563; font-weight: 500; margin: 0 0 24px;\">A customer needs your help</p>\n\n              <!-- Ticket Summary -->\n              <table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"100%\" style=\"background: #F9FAFB; border-radius: 8px; padding: 20px; margin-bottom: 24px;\">\n                <tr>\n                  <td style=\"font-size: 14px; color: #8B5CF6; font-weight: 600;\">Ticket #{{ticket_id}}</td>\n                </tr>\n                <tr>\n                  <td style=\"font-size: 16px; font-weight: 600; color: #1F2937; padding-top: 10px;\">{{ticket_title}}</td>\n                </tr>\n                <tr>\n                  <td style=\"font-size: 14px; color: #6B7280; padding-top: 8px; padding-bottom: 12px;\">{{ticket_description}}</td>\n                </tr>\n                <tr>\n                  <td style=\"font-size: 13px; color: #6B7280;\">\n                    <strong>Priority:</strong> <span style=\"color: #DC2626;\">{{ticket_priority}}</span><br />\n                    <strong>Customer:</strong> {{customer_name}}\n                  </td>\n                </tr>\n              </table>\n\n              <!-- AI Assessment -->\n              <div style=\"background: #F0FDF4; border-radius: 6px; padding: 16px; margin-bottom: 32px;\">\n                <h3 style=\"font-size: 14px; font-weight: 600; color: #15803D; margin: 0 0 8px;\">🤖 AI Assessment</h3>\n                <p style=\"font-size: 14px; color: #166534; margin: 0;\">{{ai_assessment}}</p>\n              </div>\n\n              <!-- CTA Button -->\n              <a href=\"{{ticket_url}}\" style=\"display: inline-block; background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%); color: #ffffff; padding: 12px 28px; text-decoration: none; font-weight: 600; border-radius: 6px; margin-bottom: 24px;\">View Ticket</a>\n\n              <!-- Metadata -->\n              <p style=\"font-size: 13px; color: #9CA3AF; margin-top: 24px;\">Customer: {{customer_email}} &nbsp;|&nbsp; Created: {{created_date}}</p>\n            </td>\n          </tr>\n\n        </table>\n      </td>\n    </tr>\n  </table>\n\n</body>\n</html>\n", "text_content": null, "template_type": "html_content", "category": "ticket-notification", "subject": "A New ticket for you!", "default_from_email": "<EMAIL>", "default_from_name": "Ajentic Support", "variables": {"name": "{{user.name}}", "email": "{{user.email}}", "assignee": "{{ticket.assignee.name}}", "ticket_id": "{{ticket.id}}", "agent_name": "{{ticket.agent.name}}", "ticket_url": "{{ticket.url}}", "company_name": "{{organization.name}}", "created_date": "{{ticket.createdAt}}", "ticket_title": "{{ticket.title}}", "ai_assessment": "{{ticket.aiAssessment}}", "customer_name": "{{ticket.customer.name}}", "ticket_status": "{{ticket.status}}", "customer_email": "{{ticket.customer.email}}", "customer_phone": "{{ticket.customer.phone}}", "organization_id": "{{organization.id}}", "ticket_priority": "{{ticket.priority}}", "ticket_description": "{{ticket.description}}"}, "required_variables": [], "is_active": true, "scope": "global", "usage_count": 0, "last_used": null, "tags": [], "version": "2.0.0", "preview_data": {}, "css_styles": null, "responsive_design": true, "analytics_enabled": true, "createdAt": "2025-08-01T05:16:02.832Z", "updatedAt": "2025-08-05T04:46:08.114Z", "publishedAt": "2025-08-05T03:40:38.253Z", "parent_template": null, "created_by_user": null, "organization": null, "createdBy": null, "updatedBy": null}, "24": {"id": 24, "name": "welcome-email", "display_name": "Production-ready welcome email with inline CSS and professional Ajentic branding", "description": "Production-ready welcome email with inline CSS and professional Ajentic branding", "provider": "postmark", "template_id": "", "html_content": "<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\" />\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n  <title>Welcome to {{company_name}}</title>\n</head>\n<body style=\"margin: 0; padding: 0; background-color: #F3F4F6; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; color: #374151;\">\n\n  <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n    <tr>\n      <td align=\"center\" style=\"padding: 40px 20px;\">\n        <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\">\n          \n          <!-- Logo -->\n          <tr>\n            <td style=\"padding: 20px 30px 0;\">\n              <img src=\"https://app-dev.ajentic.com/assets/images/logo/ajentic-logo.png\" alt=\"{{company_name}}\" style=\"height: 36px; width: auto;\" />\n            </td>\n          </tr>\n\n          <!-- Header Image -->\n          <tr>\n            <td align=\"center\" style=\"padding: 20px 30px 0;\">\n              <img src=\"https://talkbase-chatbot-images.s3.ap-south-1.amazonaws.com/emailers/welcome-email-ajentic.png\" alt=\"Welcome\" style=\"max-width: 180px; height: auto; display: block; margin: 0 auto;\" />\n            </td>\n          </tr>\n\n          <!-- Content -->\n          <tr>\n            <td style=\"padding: 30px 40px;\">\n                <h1 style=\"font-size: 22px; font-weight: 700; color: #111827; margin-bottom: 12px;\">Welcome to {{company_name}}!</h1>\n                <p style=\"font-size: 16px; color: #4B5563; font-weight: 500; margin-bottom: 24px;\">Your smartest teammate is now online.</p>\n\n                <p style=\"font-size: 15px; color: #6B7280; margin-bottom: 32px;\">Hi {{name}}, your account is now active! Get ready to experience AI-powered productivity like never before.</p>\n\n                <!-- Benefits -->\n                <div style=\"background: #F9FAFB; border-radius: 8px; padding: 24px; margin-bottom: 32px;\">\n                <h3 style=\"font-size: 16px; font-weight: 600; color: #374151; margin-bottom: 16px; text-align: center;\">Here’s what you can do:</h3>\n                <ul style=\"margin: 0; padding-left: 20px; color: #4B5563; line-height: 1.8;\">\n                    <li>🤖 Automate repetitive tasks with AI workflows</li>\n                    <li>📊 Get smart insights and analytics</li>\n                    <li>⚡ Boost productivity with intelligent assistance</li>\n                </ul>\n                </div>\n\n                <!-- CTA Button (centered) -->\n                <div style=\"text-align: center; margin-bottom: 24px;\">\n                <a href=\"{{dashboard_url}}\" style=\"display: inline-block; background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%); color: #ffffff; padding: 12px 28px; text-decoration: none; font-weight: 600; border-radius: 6px; font-size: 15px;\">\n                    Launch Dashboard\n                </a>\n                </div>\n\n                <!-- Support -->\n                <p style=\"font-size: 13px; color: #9CA3AF;\">Need help? Contact <a href=\"mailto:{{support_email}}\" style=\"color: #8B5CF6; text-decoration: none;\">{{support_email}}</a></p>\n            </td>\n            </tr>\n\n        </table>\n      </td>\n    </tr>\n  </table>\n\n</body>\n</html>\n", "text_content": null, "template_type": "html_content", "category": "welcome", "subject": "Welcome to Ajentic! Your smartest teammate is now online", "default_from_email": "<EMAIL>", "default_from_name": "Ajentic Team", "variables": {"name": "{{user.name}}", "email": "{{user.email}}", "user_id": "{{user.id}}", "features": "{{app.keyFeatures}}", "company_name": "{{organization.name}}", "dashboard_url": "{{app.dashboardUrl}}", "support_email": "{{organization.supportEmail}}", "organization_id": "{{organization.id}}"}, "required_variables": [], "is_active": true, "scope": "global", "usage_count": 1, "last_used": "2025-08-05T03:42:20.728Z", "tags": [], "version": "2.0.0", "preview_data": {}, "css_styles": null, "responsive_design": true, "analytics_enabled": true, "createdAt": "2025-08-01T05:16:02.870Z", "updatedAt": "2025-08-05T04:41:05.681Z", "publishedAt": "2025-08-05T03:41:28.666Z", "parent_template": null, "created_by_user": null, "organization": null, "createdBy": null, "updatedBy": null}}}}