{"name": "talkbase-auth-project", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi"}, "dependencies": {"@amplitude/analytics-node": "^1.3.5", "@slack/web-api": "^7.0.4", "@strapi/plugin-i18n": "4.5.4", "@strapi/plugin-sentry": "^4.11.3", "@strapi/plugin-users-permissions": "4.11.3", "@strapi/provider-email-sendgrid": "^4.11.3", "@strapi/provider-upload-aws-s3": "^4.15.4", "@strapi/strapi": "4.11.3", "@strapi/utils": "^4.11.3", "axios": "^1.4.0", "better-sqlite3": "7.4.6", "chance": "^1.1.9", "cheerio": "^1.0.0-rc.12", "crypto-js": "^4.1.1", "form-data": "^4.0.0", "install": "^0.13.0", "koa-ratelimit": "^5.0.1", "lodash": "^4.17.21", "mailgun-js": "^0.22.0", "mailgun.js": "^12.0.3", "mysql2": "^3.5.2", "npm": "^9.7.1", "passport-google-oauth2": "0.2.0", "phone": "^3.1.32", "plivo": "^4.34.0", "postmark": "^4.0.5", "save": "^2.9.0", "strapi-plugin-import-export-entries": "^1.21.0", "strapi-plugin-measurement-protocol": "^1.0.1", "strapi-plugin-migrations": "^1.0.4", "strapi-postmark-provider": "file:./providers/strapi-postmark-provider", "strapi-stripe": "^3.3.0", "uuid": "^9.0.0", "whatsapp": "^0.0.5-Alpha", "yup": "^1.4.0"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "bf67b19e-c003-451d-82e5-92b05ab3c0aa"}, "engines": {"node": ">=14.19.1 <=18.x.x", "npm": ">=6.0.0"}, "license": "MIT"}