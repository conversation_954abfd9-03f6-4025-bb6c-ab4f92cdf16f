{"info": {"name": "Contact Bookings & Email Testing", "description": "Collection for testing contact bookings endpoint and verifying ticket email functionality", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:1337", "description": "Base URL for Strapi API"}, {"key": "auth_token", "value": "", "description": "JWT token for authentication"}, {"key": "query_id", "value": "", "description": "Answer/Query ID for testing"}, {"key": "session_id", "value": "", "description": "Session ID for testing"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "item": [{"name": "Authentication", "item": [{"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('auth_token', response.jwt);", "    pm.test('Login successful', function () {", "        pm.expect(response.jwt).to.not.be.undefined;", "    });", "} else {", "    pm.test('<PERSON><PERSON> failed', function () {", "        pm.expect.fail('Authentication failed');", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"identifier\": \"<EMAIL>\",\n  \"password\": \"your-password\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/local", "host": ["{{base_url}}"], "path": ["api", "auth", "local"]}}}]}, {"name": "Contact Bookings Tests", "item": [{"name": "Create Contact Booking - With Query ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has booking data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.not.be.undefined;", "    pm.expect(response.data.id).to.not.be.undefined;", "});", "", "pm.test('Email should be triggered', function () {", "    // This test verifies the booking was created", "    // Email verification should be done manually or via email logs", "    pm.expect(pm.response.code).to.equal(200);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"data\": {\n    \"user_name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"timezone\": \"America/New_York\",\n    \"date_time\": \"2024-01-15T10:00:00.000Z\",\n    \"communication_channel\": \"email\",\n    \"phone_number\": null,\n    \"channel_link\": \"<EMAIL>\",\n    \"query\": {{query_id}}\n  }\n}"}, "url": {"raw": "{{base_url}}/api/contact-bookings", "host": ["{{base_url}}"], "path": ["api", "contact-bookings"]}}}, {"name": "Create Contact Booking - With Session ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has booking data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.not.be.undefined;", "    pm.expect(response.data.id).to.not.be.undefined;", "});", "", "pm.test('Ticket creation should be triggered', function () {", "    // Verify booking creation which triggers ticket creation", "    pm.expect(pm.response.code).to.equal(200);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"data\": {\n    \"user_name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"timezone\": \"America/Los_Angeles\",\n    \"date_time\": \"2024-01-16T14:00:00.000Z\",\n    \"communication_channel\": \"google_meet\",\n    \"phone_number\": null,\n    \"channel_link\": \"https://meet.google.com/abc-defg-hij\",\n    \"session_id\": \"{{session_id}}\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/contact-bookings", "host": ["{{base_url}}"], "path": ["api", "contact-bookings"]}}}, {"name": "Create Contact Booking - WhatsApp Flow", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('WhatsApp number should be extracted', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.not.be.undefined;", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"data\": {\n    \"user_name\": \"WhatsApp User\",\n    \"timezone\": \"UTC\",\n    \"date_time\": \"2024-01-17T09:00:00.000Z\",\n    \"communication_channel\": \"whatsapp\",\n    \"channel_link\": \"https://wa.me/1234567890\",\n    \"query\": {{query_id}}\n  }\n}"}, "url": {"raw": "{{base_url}}/api/contact-bookings", "host": ["{{base_url}}"], "path": ["api", "contact-bookings"]}}}]}, {"name": "Test Data Setup", "item": [{"name": "Get Sample Answer/Query ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.length > 0) {", "        pm.collectionVariables.set('query_id', response.data[0].id);", "        console.log('Set query_id to:', response.data[0].id);", "    }", "}"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/answers?pagination[limit]=1&populate=knowledgebase", "host": ["{{base_url}}"], "path": ["api", "answers"], "query": [{"key": "pagination[limit]", "value": "1"}, {"key": "populate", "value": "knowledgebase"}]}}}, {"name": "Create Test Session", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.collectionVariables.set('session_id', response.data.attributes.session || response.data.id);", "        console.log('Set session_id to:', response.data.attributes.session || response.data.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"data\": {\n    \"question\": \"Test question for booking\",\n    \"answer\": \"Test answer for booking\",\n    \"session\": \"test-session-\" + Date.now(),\n    \"api_source\": \"postman-test\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/answers", "host": ["{{base_url}}"], "path": ["api", "answers"]}}}]}, {"name": "Email Verification", "item": [{"name": "Get Email Logs", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Email logs retrieved', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.be.an('array');", "});", "", "pm.test('Check for ticket notification emails', function () {", "    const response = pm.response.json();", "    const ticketEmails = response.data.filter(log => log.attributes.emailType === 'ticket-notification');", "    console.log('Ticket notification emails found:', ticketEmails.length);", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/email-logs?sort=createdAt:desc&pagination[limit]=50", "host": ["{{base_url}}"], "path": ["api", "email-logs"], "query": [{"key": "sort", "value": "createdAt:desc"}, {"key": "pagination[limit]", "value": "50"}]}}}, {"name": "Get Recent Ticket Emails", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Ticket emails found', function () {", "    const response = pm.response.json();", "    pm.expect(response.data.length).to.be.greaterThan(0);", "    ", "    response.data.forEach(log => {", "        console.log('Email:', {", "            id: log.id,", "            to: log.attributes.toEmails,", "            subject: log.attributes.subject,", "            status: log.attributes.status,", "            createdAt: log.attributes.createdAt", "        });", "    });", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/email-logs?filters[emailType][$eq]=ticket-notification&sort=createdAt:desc&pagination[limit]=10", "host": ["{{base_url}}"], "path": ["api", "email-logs"], "query": [{"key": "filters[emailType][$eq]", "value": "ticket-notification"}, {"key": "sort", "value": "createdAt:desc"}, {"key": "pagination[limit]", "value": "10"}]}}}]}, {"name": "Error <PERSON>", "item": [{"name": "Create Booking - Missing Email and Query", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Error message about empty query and session_id', function () {", "    const response = pm.response.json();", "    pm.expect(response.error.message).to.include('empty');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"data\": {\n    \"user_name\": \"Test User\",\n    \"timezone\": \"UTC\",\n    \"date_time\": \"2024-01-15T10:00:00.000Z\",\n    \"communication_channel\": \"email\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/contact-bookings", "host": ["{{base_url}}"], "path": ["api", "contact-bookings"]}}}, {"name": "Create Booking - Missing Email for Non-WhatsApp", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Error message about required email', function () {", "    const response = pm.response.json();", "    pm.expect(response.error.message).to.include('Email is required');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"data\": {\n    \"user_name\": \"Test User\",\n    \"timezone\": \"UTC\",\n    \"date_time\": \"2024-01-15T10:00:00.000Z\",\n    \"communication_channel\": \"email\",\n    \"query\": {{query_id}}\n  }\n}"}, "url": {"raw": "{{base_url}}/api/contact-bookings", "host": ["{{base_url}}"], "path": ["api", "contact-bookings"]}}}]}, {"name": "Ticket Verification", "item": [{"name": "Get Recent Scrumboard Cards (Tickets)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Tickets found', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.be.an('array');", "    ", "    response.data.forEach(ticket => {", "        console.log('Ticket:', {", "            id: ticket.id,", "            title: ticket.attributes.title,", "            description: ticket.attributes.description,", "            createdAt: ticket.attributes.createdAt", "        });", "    });", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/scrumboard-cards?sort=createdAt:desc&pagination[limit]=10&populate=contact_booking", "host": ["{{base_url}}"], "path": ["api", "scrumboard-cards"], "query": [{"key": "sort", "value": "createdAt:desc"}, {"key": "pagination[limit]", "value": "10"}, {"key": "populate", "value": "contact_booking"}]}}}]}]}