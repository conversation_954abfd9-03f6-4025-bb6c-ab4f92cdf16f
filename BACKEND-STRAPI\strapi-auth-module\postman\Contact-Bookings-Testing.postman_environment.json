{"id": "contact-bookings-testing-env", "name": "Contact Bookings Testing Environment", "values": [{"key": "base_url", "value": "http://localhost:1337", "description": "Base URL for Strapi API", "enabled": true}, {"key": "auth_token", "value": "", "description": "JWT authentication token (auto-populated)", "enabled": true}, {"key": "query_id", "value": "", "description": "Sample answer/query ID for testing (auto-populated)", "enabled": true}, {"key": "session_id", "value": "", "description": "Test session ID (auto-populated)", "enabled": true}, {"key": "test_email", "value": "<EMAIL>", "description": "Email address to receive test notifications", "enabled": true}, {"key": "test_user_email", "value": "<EMAIL>", "description": "Update this with your login email", "enabled": true}, {"key": "test_user_password", "value": "your-password", "description": "Update this with your login password", "enabled": true}], "_postman_variable_scope": "environment"}