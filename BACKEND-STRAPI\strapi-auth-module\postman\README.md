# Contact Bookings & Email Testing - Postman Collection

This directory contains comprehensive Postman testing resources for verifying the contact bookings endpoint and email notification functionality.

## 📁 Files Overview

### Core Testing Files
- **`Contact-Bookings-Email-Testing.postman_collection.json`** - Main Postman collection with all test scenarios
- **`Contact-Bookings-Testing.postman_environment.json`** - Environment variables for easy configuration
- **`TESTING_GUIDE.md`** - Comprehensive testing guide with step-by-step instructions
- **`verify-setup.js`** - Node.js script to verify server setup before testing

## 🚀 Quick Start

### 1. Import Collection & Environment
1. Open Postman
2. Import `Contact-Bookings-Email-Testing.postman_collection.json`
3. Import `Contact-Bookings-Testing.postman_environment.json`
4. Select the imported environment

### 2. Configure Authentication
Update these variables in the environment or directly in requests:
- `test_user_email`: Your Strapi login email
- `test_user_password`: Your Strapi login password

### 3. Run Tests
Execute requests in this order:
1. **Authentication** → Login User
2. **Test Data Setup** → Get Sample Answer/Query ID → Create Test Session  
3. **Contact Bookings Tests** → Run all booking scenarios
4. **Email Verification** → Check email logs and delivery
5. **Ticket Verification** → Verify ticket creation

## 📧 Email Testing Focus

This collection specifically tests:
- ✅ **Ticket Email Notifications** when bookings are created
- ✅ **Email Template Rendering** with proper variables
- ✅ **Email Delivery** to correct recipients (`<EMAIL>`)
- ✅ **Email Logging** for debugging and verification
- ✅ **Organization-wide Notifications** to team members

## 🧪 Test Scenarios Included

### Contact Booking Tests
1. **Booking with Query ID** - Standard email booking flow
2. **Booking with Session ID** - Session-based booking flow  
3. **WhatsApp Flow** - Phone number extraction from WhatsApp links
4. **Error Handling** - Missing required fields validation

### Email Verification Tests
1. **Email Logs Retrieval** - Get all recent email activity
2. **Ticket Email Filtering** - Filter for ticket notification emails
3. **Email Status Verification** - Check delivery status and errors

### Ticket Verification Tests
1. **Ticket Creation** - Verify tickets are created automatically
2. **Ticket Content** - Check AI assessment and customer details
3. **Booking Relationship** - Verify ticket-booking connections

## 🔧 Setup Verification

Before testing, run the setup verification:

```bash
cd BACKEND-STRAPI/strapi-auth-module/postman
node verify-setup.js
```

This checks:
- ✅ Server connectivity
- ✅ Required content types
- ✅ Environment variables
- ✅ Email configuration

## 📊 Expected Results

### Successful Booking Creation
```json
{
  "data": {
    "id": 123,
    "attributes": {
      "user_name": "John Doe",
      "email": "<EMAIL>",
      "communication_channel": "email",
      "createdAt": "2024-01-15T10:00:00.000Z"
    }
  }
}
```

### Email Log Entry
```json
{
  "data": {
    "id": 456,
    "attributes": {
      "emailType": "ticket-notification",
      "toEmails": ["<EMAIL>"],
      "fromEmail": "<EMAIL>",
      "status": "sent",
      "templateName": "ticket-notification"
    }
  }
}
```

### Ticket Creation
```json
{
  "data": {
    "id": 789,
    "attributes": {
      "title": "Customer ticket",
      "description": "A ticket was created from...",
      "ai_assesment": "AI assessment of the conversation",
      "contact_booking": { "data": { "id": 123 } }
    }
  }
}
```

## 🐛 Troubleshooting

### Common Issues

**No Emails Received**
- Check Postmark API configuration
- Verify recipient email address
- Check email logs for failures
- Confirm email service is enabled

**Authentication Errors**  
- Update login credentials in environment
- Check user permissions
- Verify JWT token validity

**Missing Test Data**
- Run "Test Data Setup" requests first
- Check if knowledgebase exists
- Verify answer/query records exist

**Tickets Not Created**
- Check scrumboard configuration
- Verify lifecycle hooks are working
- Check server logs for errors

### Debug Steps
1. Check Strapi server logs
2. Verify Postmark configuration
3. Test email service manually
4. Check database records

## 📈 Success Metrics

After running all tests, you should see:
- ✅ All booking requests return 200 OK
- ✅ Tickets created automatically via lifecycle hooks
- ✅ Emails sent to `<EMAIL>`
- ✅ Email logs show "sent" status
- ✅ Error handling works for invalid requests

## 🔗 Related Documentation

- [TESTING_GUIDE.md](./TESTING_GUIDE.md) - Detailed testing instructions
- [Email Service Documentation](../src/helpers/email-service.js)
- [Contact Booking Controller](../src/api/contact-booking/controllers/contact-booking.js)
- [Ticket Creation Helper](../src/helpers/create-scrumboard-ticket.js)

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section in TESTING_GUIDE.md
2. Verify server logs for detailed error messages
3. Ensure all environment variables are properly configured
4. Test individual components (auth, booking creation, email service) separately

---

**Happy Testing! 🎉**

This collection provides comprehensive coverage of the contact bookings and email notification flow, helping ensure your ticket system works reliably.
