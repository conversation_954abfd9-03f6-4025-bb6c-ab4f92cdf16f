# Contact Bookings & Email Testing Guide - UPDATED

This guide will help you test the contact bookings endpoint and verify that ticket emails are working correctly using Postman. **This version includes fixes for common issues and comprehensive debugging steps.**

## 🚨 Recent Fixes Applied

### Issues Fixed:
1. **Scrumboard Null Reference Error** - Added automatic scrumboard creation if missing
2. **Policy Middleware Failures** - Added graceful handling of external API timeouts
3. **External API Connection Issues** - Added timeout handling and fallback behavior
4. **Missing Test Data** - Enhanced test data setup with proper relationships

### Key Improvements:
- ✅ Automatic scrumboard creation for knowledgebases
- ✅ Graceful external API failure handling
- ✅ Enhanced error logging and debugging
- ✅ Comprehensive test data validation
- ✅ Better error recovery mechanisms

## Prerequisites

1. **Strapi Server Running**: Ensure your Strapi server is running on `http://localhost:1337`
2. **Email Configuration**: Verify Postmark email service is configured with:
   - API Key: `d11be8bb-6c68-43c6-89d2-dd0a41e8638f`
   - From Email: `<EMAIL>`
   - From Name: `Podycy Team`
3. **Test Email**: You should receive emails at `<EMAIL>`
4. **External API**: TALKBASE_BASE_URL should be configured (but will gracefully fail if unavailable)

## Setup Instructions

### 1. Import Postman Collection

1. Open Postman
2. Click "Import" button
3. Select the `Contact-Bookings-Email-Testing.postman_collection.json` file
4. The collection will be imported with all test scenarios

### 2. Configure Environment Variables

The collection uses these variables (automatically managed):
- `base_url`: Set to `http://localhost:1337`
- `auth_token`: Automatically set after login
- `query_id`: Automatically set from test data
- `session_id`: Automatically set from test data

### 3. Update Authentication

In the "Login User" request, update the credentials:
```json
{
  "identifier": "<EMAIL>",
  "password": "your-password"
}
```

## 🔄 Updated Testing Workflow

### Step 1: Authentication
1. Run "Login User" request
2. Verify the `auth_token` is set automatically
3. All subsequent requests will use this token

### Step 2: Enhanced Test Data Setup & Validation
**NEW: Comprehensive setup with validation**

1. **Health Check** - Verify server is running
2. **Create Test Organization** - Creates organization with proper structure
3. **Create Test Knowledgebase** - Creates KB with automatic scrumboard
4. **Verify Knowledgebase has Scrumboard** - Validates scrumboard relationship
5. **Get Sample Answer/Query ID** - Gets existing data or creates new
6. **Create Test Answer with Knowledgebase** - Creates proper test data with relationships
7. **Validate Test Data Setup** - Confirms all required data is ready

**Key Improvements:**
- ✅ Automatic creation of missing components
- ✅ Validation of all relationships
- ✅ Proper error handling and recovery
- ✅ Detailed logging for debugging

### Step 3: Test Contact Bookings

#### Test Scenario 1: Booking with Query ID
```json
{
  "data": {
    "user_name": "John Doe",
    "email": "<EMAIL>",
    "timezone": "America/New_York",
    "date_time": "2024-01-15T10:00:00.000Z",
    "communication_channel": "email",
    "phone_number": null,
    "channel_link": "<EMAIL>",
    "query": {{query_id}}
  }
}
```

**Expected Results:**
- ✅ Status: 200 OK
- ✅ Booking created successfully
- ✅ Ticket created automatically (via lifecycle hook)
- ✅ Email sent to organization members

#### Test Scenario 2: Booking with Session ID
```json
{
  "data": {
    "user_name": "Jane Smith",
    "email": "<EMAIL>",
    "timezone": "America/Los_Angeles",
    "date_time": "2024-01-16T14:00:00.000Z",
    "communication_channel": "google_meet",
    "channel_link": "https://meet.google.com/abc-defg-hij",
    "session_id": "{{session_id}}"
  }
}
```

**Expected Results:**
- ✅ Status: 200 OK
- ✅ Booking created with session reference
- ✅ Ticket created with session context
- ✅ Email notification sent

#### Test Scenario 3: WhatsApp Flow
```json
{
  "data": {
    "user_name": "WhatsApp User",
    "timezone": "UTC",
    "date_time": "2024-01-17T09:00:00.000Z",
    "communication_channel": "whatsapp",
    "channel_link": "https://wa.me/1234567890",
    "query": {{query_id}}
  }
}
```

**Expected Results:**
- ✅ Status: 200 OK
- ✅ Phone number extracted from WhatsApp link
- ✅ No email required for WhatsApp bookings
- ✅ Ticket created with phone context

### Step 4: Verify Email Functionality

#### Check Email Logs
1. Run "Get Email Logs" to see all recent emails
2. Run "Get Recent Ticket Emails" to filter ticket notifications
3. Look for emails with `emailType: "ticket-notification"`

#### Verify Email Content
Check that emails contain:
- ✅ Correct recipient: `<EMAIL>`
- ✅ From address: `<EMAIL>`
- ✅ Subject: "A New ticket for you!"
- ✅ Ticket details (title, description, customer info)
- ✅ AI assessment if available
- ✅ Proper template rendering

### Step 5: Verify Ticket Creation

1. Run "Get Recent Scrumboard Cards (Tickets)"
2. Verify tickets were created with:
   - ✅ Proper title and description
   - ✅ Link to contact booking
   - ✅ AI assessment from conversation
   - ✅ Correct timestamps

## Error Handling Tests

### Test Missing Required Fields
1. Run "Create Booking - Missing Email and Query"
   - Expected: 400 Bad Request
   - Error: "Both query and session_id can't be empty"

2. Run "Create Booking - Missing Email for Non-WhatsApp"
   - Expected: 400 Bad Request  
   - Error: "Email is required"

## Email Verification Checklist

### Manual Email Verification
1. **Check Inbox**: Look for emails at `<EMAIL>`
2. **Verify Sender**: From `<EMAIL>` (Podycy Team)
3. **Check Content**: 
   - Ticket title and description
   - Customer information
   - Agent/knowledgebase name
   - Meeting/contact details
   - AI assessment (if available)

### Database Verification
1. **Email Logs**: Check `email_logs` table for entries
2. **Status**: Verify emails have `status: "sent"`
3. **Template**: Confirm `templateName: "ticket-notification"`
4. **Recipients**: Verify correct email addresses

## 🔧 Troubleshooting - Updated with Recent Fixes

### Recently Fixed Issues

#### 1. **"Cannot read properties of null (reading 'scrumboard')" - FIXED ✅**
**Problem**: Knowledgebase missing scrumboard relationship
**Solution Applied**:
- Automatic scrumboard creation if missing
- Proper error handling and recovery
- Enhanced validation in test setup

**What to check now**:
- Run the "Verify Knowledgebase has Scrumboard" test
- Check server logs for "✅ Created scrumboard for knowledgebase" messages
- Verify scrumboard lists are created properly

#### 2. **"Cannot read properties of undefined (reading 'data')" - FIXED ✅**
**Problem**: External API (TALKBASE_BASE_URL) connection failures
**Solution Applied**:
- Added timeout handling (10 seconds)
- Graceful fallback when external API is unavailable
- Request proceeds even if external API fails

**What to check now**:
- Check server logs for "⚠️ Failed to update organization usage from external API"
- Verify requests still succeed even with external API issues
- Monitor "🔄 Proceeding with request despite external API failure" messages

#### 3. **External API Timeout Errors - FIXED ✅**
**Problem**: Network timeouts to external services
**Solution Applied**:
- 15-second timeout for AI assessment calls
- Default ticket creation when AI service unavailable
- Proper error logging without blocking ticket creation

**What to check now**:
- Look for "⚠️ Failed to generate AI assessment" in logs
- Verify tickets are still created with default content
- Check "🔄 Proceeding with default ticket creation" messages

### Common Issues (Updated)

1. **No Emails Received**
   - ✅ **NEW**: Check email logs via Postman collection
   - Check Postmark API key configuration
   - Verify email service is enabled
   - Check email logs for failures
   - Confirm recipient email is correct

2. **Authentication Errors**
   - Update login credentials in environment variables
   - Check if user has proper permissions
   - Verify JWT token is valid
   - ✅ **NEW**: Use the enhanced test data setup

3. **Missing Test Data - ENHANCED ✅**
   - ✅ **NEW**: Run the complete "Test Data Setup & Validation" folder
   - ✅ **NEW**: Automatic creation of missing organizations/knowledgebases
   - Check if knowledgebase exists and has scrumboard
   - Verify answer/query records exist with proper relationships

4. **Ticket Not Created - ENHANCED ✅**
   - ✅ **NEW**: Automatic scrumboard creation if missing
   - ✅ **NEW**: Automatic list creation for scrumboards
   - Check server logs for detailed error messages
   - Verify lifecycle hooks are working
   - Check if external AI service is causing delays (now handled gracefully)

### Debug Steps

1. **Check Server Logs**: Look for email service errors
2. **Verify Configuration**: Ensure Postmark settings are correct
3. **Test Email Service**: Try sending a test email manually
4. **Check Database**: Verify records are created properly

## Expected Email Template

The ticket notification email should include:
- Header with company branding
- Ticket information (ID, title, description)
- Customer details (name, email, phone)
- Agent/knowledgebase information
- AI assessment and suggested actions
- Meeting/contact channel details
- Professional footer with support information

## Success Criteria

✅ **Booking Creation**: All booking scenarios create records successfully
✅ **Ticket Generation**: Tickets are automatically created via lifecycle hooks
✅ **Email Delivery**: Notification emails are sent to correct recipients
✅ **Email Content**: Templates render correctly with all variables
✅ **Error Handling**: Invalid requests return appropriate error messages
✅ **Logging**: All email activities are properly logged for debugging

## Next Steps

After successful testing:
1. Test with real knowledgebase data
2. Verify organization-wide notifications
3. Test different communication channels
4. Validate email template customizations
5. Test with production email settings
