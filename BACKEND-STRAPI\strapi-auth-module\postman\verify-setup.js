/**
 * Setup Verification Script for Contact Bookings Email Testing
 * 
 * This script helps verify that your Strapi server is properly configured
 * for testing contact bookings and email functionality.
 * 
 * Run this in Node.js to check your setup before using Postman tests.
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:1337';
const TEST_EMAIL = '<EMAIL>';

async function verifySetup() {
    console.log('🔍 Verifying Contact Bookings Email Testing Setup...\n');

    try {
        // 1. Check if server is running
        console.log('1. Checking if Strapi server is running...');
        const healthCheck = await axios.get(`${BASE_URL}/api`);
        console.log('✅ Server is running');

        // 2. Check if email service is configured
        console.log('\n2. Checking email configuration...');
        // This would require authentication, so we'll skip for now
        console.log('⚠️  Email configuration check requires authentication');

        // 3. Check if required content types exist
        console.log('\n3. Checking required content types...');
        
        try {
            const answersCheck = await axios.get(`${BASE_URL}/api/answers?pagination[limit]=1`);
            console.log('✅ Answers content type exists');
        } catch (error) {
            console.log('❌ Answers content type not accessible');
        }

        try {
            const bookingsCheck = await axios.get(`${BASE_URL}/api/contact-bookings?pagination[limit]=1`);
            console.log('✅ Contact-bookings content type exists');
        } catch (error) {
            console.log('❌ Contact-bookings content type not accessible');
        }

        try {
            const emailLogsCheck = await axios.get(`${BASE_URL}/api/email-logs?pagination[limit]=1`);
            console.log('✅ Email-logs content type exists');
        } catch (error) {
            console.log('❌ Email-logs content type not accessible');
        }

        try {
            const ticketsCheck = await axios.get(`${BASE_URL}/api/scrumboard-cards?pagination[limit]=1`);
            console.log('✅ Scrumboard-cards content type exists');
        } catch (error) {
            console.log('❌ Scrumboard-cards content type not accessible');
        }

        console.log('\n📋 Setup Verification Summary:');
        console.log('✅ Server is running and accessible');
        console.log('⚠️  Content type accessibility requires authentication');
        console.log('\n📝 Next Steps:');
        console.log('1. Import the Postman collection');
        console.log('2. Update login credentials in the "Login User" request');
        console.log('3. Run the authentication request first');
        console.log('4. Run the test data setup requests');
        console.log('5. Execute the contact booking tests');
        console.log('6. Verify emails are received at:', TEST_EMAIL);

    } catch (error) {
        console.log('❌ Server connection failed:', error.message);
        console.log('\n🔧 Troubleshooting:');
        console.log('1. Make sure Strapi server is running on port 1337');
        console.log('2. Check if the server URL is correct');
        console.log('3. Verify no firewall is blocking the connection');
    }
}

// Environment variables check
function checkEnvironmentVariables() {
    console.log('\n🌍 Environment Variables Check:');
    
    const requiredEnvVars = [
        'POSTMARK_API_KEY',
        'POSTMARK_FROM_EMAIL', 
        'POSTMARK_FROM_NAME'
    ];

    requiredEnvVars.forEach(envVar => {
        if (process.env[envVar]) {
            console.log(`✅ ${envVar} is set`);
        } else {
            console.log(`❌ ${envVar} is not set`);
        }
    });

    console.log('\n📧 Expected Email Configuration:');
    console.log('POSTMARK_API_KEY=************************************');
    console.log('POSTMARK_FROM_EMAIL=<EMAIL>');
    console.log('POSTMARK_FROM_NAME=Podycy Team');
}

// Email template verification
function checkEmailTemplates() {
    console.log('\n📧 Email Template Check:');
    console.log('Required templates:');
    console.log('✓ ticket-notification template');
    console.log('✓ Template should include variables for:');
    console.log('  - ticket_title, ticket_description');
    console.log('  - customer_name, customer_email');
    console.log('  - agent_name, ticket_url');
    console.log('  - ai_assessment, created_date');
}

// Run all checks
async function runAllChecks() {
    await verifySetup();
    checkEnvironmentVariables();
    checkEmailTemplates();
    
    console.log('\n🚀 Ready to test! Import the Postman collection and start testing.');
}

// Execute if run directly
if (require.main === module) {
    runAllChecks().catch(console.error);
}

module.exports = {
    verifySetup,
    checkEnvironmentVariables,
    checkEmailTemplates,
    runAllChecks
};
