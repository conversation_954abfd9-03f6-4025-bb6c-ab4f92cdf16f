"use strict";

/**
 * Scrumboard card usage lifecycle
 */
const _ = require("lodash");
const emailService = require("../../../../helpers/email-service");

module.exports = {
  beforeCreate: async (data) => {
    try {
      if (!data.params.data.dueDate) {
        data.params.data['dueDate'] = new Date(); // Set current date and time
      }
      const board = await strapi.query('api::scrumboard.scrumboard').findOne({
        where: {
          id: data.params.data.scrumboard,
        },
      });
      const latest = await strapi.query('api::scrumboard-card.scrumboard-card').findOne({
        where: {
          scrumboard: data.params.data.scrumboard,
        },
        orderBy: { id: 'desc' },
      });
      var count=1;
      if(latest){
        count = latest.id+1;
      }
      data.params.data['ticketId']=`${board.title.substring(0, 2).toUpperCase()}-${count}`;
    } catch (e) {
      console.log(e);
    }
  },

  afterCreate: async (event) => {
    try {
      const { result } = event;

      // Get the ticket with populated data for notifications
      const ticket = await strapi.entityService.findOne('api::scrumboard-card.scrumboard-card', result.id, {
        populate: {
          scrumboard: {
            populate: {
              knowledgebase: {
                populate: {
                  organization: {
                    populate: ['users']
                  }
                }
              }
            }
          },
          contact_booking: true
        }
      });

      // Only send notifications if this ticket is associated with a knowledgebase/organization
      if (ticket?.scrumboard?.knowledgebase?.organization?.id) {
        const organizationId = ticket.scrumboard.knowledgebase.organization.id;

        // Prepare ticket data for notification
        const ticketData = {
          title: ticket.title || 'New Support Ticket',
          description: ticket.description || 'A new support ticket has been created.',
          ticketId: ticket.ticketId || `TICKET-${ticket.id}`,
          agent_name: ticket.scrumboard.knowledgebase?.name || 'AI Assistant',
          customer_email: ticket.contact_booking?.email || 'Unknown',
          customer_phone: ticket.contact_booking?.phone || null,
          customer_name: ticket.contact_booking?.name || null,
          ticket_url: ticket.contact_booking?.channel_link || `${process.env.FRONTEND_URL}/tickets/${ticket.id}`,
          priority: 'normal', // Default priority
          assignee: null,
          ai_assesment: ticket.ai_assesment || null,
          created_date: new Date().toLocaleString(),
          ticketId_system: ticket.id
        };

        // Send organization-wide notification
        await emailService.sendOrganizationTicketNotification({
          organizationId,
          ticketData,
          excludeUserIds: [] // Could exclude specific users if needed
        });

        strapi.log.info('✅ Ticket notification sent via lifecycle hook', {
          ticketId: ticket.ticketId,
          organizationId,
          title: ticket.title
        });
      }
    } catch (error) {
      // Don't throw error to avoid breaking ticket creation
      strapi.log.error('❌ Failed to send ticket notification in lifecycle hook', {
        ticketId: event.result?.id,
        error: error.message
      });
    }
  }
};
