/**
 * Optimized Email Service with External Templates
 * Uses Strapi's built-in email with JSON template files
 */

const fs = require('fs');
const path = require('path');

// Template cache for performance
const templateCache = new Map();

/**
 * Load and cache email template (database first, JSON fallback)
 */
async function loadTemplate(templateName) {
  // Check cache first
  if (templateCache.has(templateName)) {
    return templateCache.get(templateName);
  }

  try {
    // Try to get from database first
    const template = await strapi.service('api::email-template.email-template').getTemplateByName(templateName);
    templateCache.set(templateName, template);
    strapi.log.info(`📧 Template loaded from database: ${templateName}`);
    return template;
  } catch (dbError) {
    // Fallback to JSON file (check new template location first)
    try {
      // Try new template location first
      let templatePath = path.join(__dirname, '../../email-templates', `${templateName}.json`);

      if (!fs.existsSync(templatePath)) {
        // Fallback to old location
        templatePath = path.join(__dirname, '../email-templates', `${templateName}.json`);
      }

      if (!fs.existsSync(templatePath)) {
        throw new Error(`Template not found in database or JSON file: ${templateName}`);
      }

      const templateData = JSON.parse(fs.readFileSync(templatePath, 'utf8'));

      // Cache the template
      templateCache.set(templateName, templateData);

      strapi.log.warn(`📧 Template loaded from JSON fallback: ${templateName}`);
      return templateData;
    } catch (jsonError) {
      strapi.log.error(`❌ Failed to load template ${templateName}:`, jsonError.message);
      throw new Error(`Template not found: ${templateName}`);
    }
  }
}

/**
 * Render template with variables
 */
function renderTemplate(template, variables = {}) {
  // Merge template variables with provided variables
  const allVariables = {
    ...template.variables,
    ...variables
  };

  let html = template.html_content || '';
  let text = template.text_content || '';
  let subject = template.subject || '';

  // Replace all variables in content
  Object.keys(allVariables).forEach(key => {
    const value = allVariables[key] || '';
    const regex = new RegExp(`{{${key}}}`, 'g');
    html = html.replace(regex, value);
    text = text.replace(regex, value);
    subject = subject.replace(regex, value);
  });

  // Handle conditional blocks {{#if condition}}...{{/if}}
  html = html.replace(/{{#if\s+(\w+)}}(.*?){{\/if}}/gs, (match, condition, content) => {
    return allVariables[condition] ? content : '';
  });
  text = text.replace(/{{#if\s+(\w+)}}(.*?){{\/if}}/gs, (match, condition, content) => {
    return allVariables[condition] ? content : '';
  });

  // Handle each loops {{#each array}}...{{/each}}
  html = html.replace(/{{#each\s+(\w+)}}(.*?){{\/each}}/gs, (match, arrayName, itemTemplate) => {
    const array = allVariables[arrayName];
    if (!Array.isArray(array)) return '';

    return array.map(item => {
      let itemContent = itemTemplate;
      Object.entries(item).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        itemContent = itemContent.replace(regex, value || '');
      });
      return itemContent;
    }).join('');
  });
  text = text.replace(/{{#each\s+(\w+)}}(.*?){{\/each}}/gs, (match, arrayName, itemTemplate) => {
    const array = allVariables[arrayName];
    if (!Array.isArray(array)) return '';

    return array.map(item => {
      let itemContent = itemTemplate;
      Object.entries(item).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        itemContent = itemContent.replace(regex, value || '');
      });
      return itemContent;
    }).join('');
  });

  return {
    html,
    text,
    subject,
    from: `${template.default_from_name} <${template.default_from_email}>`
  };
}

/**
 * Clear template cache (useful for development)
 */
function clearTemplateCache() {
  templateCache.clear();
  strapi.log.info('📧 Template cache cleared');
}

/**
 * Get priority color for email styling
 */
function getPriorityColor(priority) {
  if (!priority) return '#666666';

  const priorityLower = priority.toLowerCase();
  if (priorityLower.includes('high') || priorityLower.includes('urgent')) return '#DC2626';
  if (priorityLower.includes('medium') || priorityLower.includes('normal')) return '#F59E0B';
  if (priorityLower.includes('low')) return '#10B981';
  return '#666666'; // Default gray
}

module.exports = {
  async sendConfirmationEmail({ email, name, confirmationUrl, userId = null, organizationId = null }) {
    let emailLog = null;
    try {
      const template = await loadTemplate('confirmation-email');
      const { html, text, subject, from } = renderTemplate(template, {
        name,
        email,
        confirmation_url: confirmationUrl,
        company_name: 'Ajentic Technologies',
        dashboard_url: 'https://app.ajentic.com/dashboard',
        support_email: '<EMAIL>',
        user_id: userId,
        organization_id: organizationId
      });

      // Send email
      const result = await strapi.plugins.email.services.email.send({
        to: email,
        subject,
        html,
        text,
        from
      });

      // Generate a unique message ID
      const messageId = result?.messageId || result?.MessageID || `conf-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Log email activity
      emailLog = await strapi.service('api::email-log.email-log').logEmail({
        messageId,
        provider: 'postmark',
        toEmails: [email],
        fromEmail: from.split('<')[1]?.replace('>', '') || from,
        fromName: from.split('<')[0]?.trim() || 'Ajentic Team',
        subject,
        templateName: template.name,
        templateData: { name, action_url: confirmationUrl },
        emailType: 'confirmation',
        organizationId,
        userId,
        metadata: { confirmationUrl }
      });

      // Update status to sent
      if (emailLog) {
        await strapi.service('api::email-log.email-log').updateEmailStatus(messageId, 'sent');
      }

      strapi.log.info('✅ Confirmation email sent:', {
        email,
        template: template.name,
        logId: emailLog?.id
      });
    } catch (error) {
      // Log failure if email log was created
      if (emailLog && emailLog.message_id) {
        await strapi.service('api::email-log.email-log').logEmailFailure(
          emailLog.message_id,
          error.message
        );
      }
      strapi.log.error('❌ Confirmation email failed:', error.message);
      throw error;
    }
  },

  async sendWelcomeEmail({ email, name }) {
    try {
      const template = await loadTemplate('welcome-email');
      const { html, text, subject, from } = renderTemplate(template, {
        name,
        email,
        dashboard_url: process.env.FRONTEND_URL || 'https://app.ajentic.com/dashboard',
        company_name: 'Ajentic Technologies',
        support_email: '<EMAIL>',
        features: 'AI-powered workflows, Smart analytics, Intelligent automation'
      });

      await strapi.plugins.email.services.email.send({
        to: email,
        subject,
        html,
        text,
        from
      });

      strapi.log.info('✅ Welcome email sent:', { email, template: template.name });
    } catch (error) {
      strapi.log.error('❌ Welcome email failed:', error.message);
      throw error;
    }
  },

  async sendPasswordResetEmail({ email, name, resetUrl }) {
    try {
      const template = await loadTemplate('reset-password-email');
      const { html, text, subject, from } = renderTemplate(template, {
        name,
        email,
        reset_url: resetUrl,
        company_name: 'Ajentic Technologies',
        support_email: '<EMAIL>',
        security_email: '<EMAIL>'
      });

      await strapi.plugins.email.services.email.send({
        to: email,
        subject,
        html,
        text,
        from
      });

      strapi.log.info('✅ Password reset email sent:', { email, template: template.name });
    } catch (error) {
      strapi.log.error('❌ Password reset email failed:', error.message);
      throw error;
    }
  },

  async sendTicketNotificationEmail({
    emails,
    name,
    title,
    description,
    agent_name,
    customer_email,
    ticket_url,
    organization_name = null,
    priority = null,
    assignee = null,
    userId = null,
    organizationId = null,
    ticketId = null,
    ai_assesment = null,
    customer_phone = null,
    customer_name = null,
    created_date = null,
    ticketId_system = null
  }) {
    const emailLogs = [];
    try {
      const template = await loadTemplate('ticket-notification');

      // Prepare template variables with dynamic backend data
      const templateVars = {
        name,
        email: emails[0], // Primary recipient email
        ticket_id: ticketId_system || ticketId || 'N/A',
        ticket_title: title,
        ticket_description: description,
        ticket_priority: priority ? priority.toLowerCase() : 'normal',
        ticket_status: 'open',
        ticket_url,
        customer_name: customer_name || 'Unknown Customer',
        customer_email,
        customer_phone: customer_phone || 'Not provided',
        agent_name,
        assignee: assignee || 'Unassigned',
        created_date: created_date || new Date().toLocaleString(),
        ai_assessment: ai_assesment || 'No AI assessment available',
        company_name: organization_name || 'Ajentic Technologies',
        organization_id: organizationId
      };

      const { html, text, subject, from } = renderTemplate(template, templateVars);

      // Send to multiple recipients with individual logging
      const emailPromises = emails.map(async (email) => {
        const messageId = `ticket-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

        // Log email before sending
        const emailLog = await strapi.service('api::email-log.email-log').logEmail({
          messageId,
          provider: 'postmark',
          toEmails: [email],
          fromEmail: from.split('<')[1]?.replace('>', '') || from,
          fromName: from.split('<')[0]?.trim() || 'Podycy Team',
          subject,
          templateName: template.name,
          templateData: templateVars,
          emailType: 'ticket-notification',
          organizationId,
          userId,
          ticketId,
          agentName: agent_name,
          customerEmail: customer_email,
          priority: priority?.toLowerCase() || null,
          metadata: {
            ticket_url,
            organization_name,
            assignee,
            recipient_name: name
          }
        });

        emailLogs.push(emailLog);

        try {
          // Send email
          const result = await strapi.plugins.email.services.email.send({
            to: email,
            subject,
            html,
            text,
            from
          });

          // Update status to sent
          await strapi.service('api::email-log.email-log').updateEmailStatus(messageId, 'sent', {
            provider_response: result
          });

          return { email, success: true, messageId };
        } catch (sendError) {
          // Log failure
          await strapi.service('api::email-log.email-log').logEmailFailure(
            messageId,
            sendError.message,
            sendError.response || null
          );
          throw sendError;
        }
      });

      const results = await Promise.all(emailPromises);

      strapi.log.info('✅ Ticket notification emails sent:', {
        recipients: emails,
        template: template.name,
        ticket_title: title,
        ticket_id: ticketId,
        agent_name,
        priority,
        logs_created: emailLogs.length,
        successful_sends: results.filter(r => r.success).length
      });

      return results;
    } catch (error) {
      strapi.log.error('❌ Ticket notification email failed:', {
        error: error.message,
        ticket_title: title,
        ticket_id: ticketId,
        agent_name,
        recipients_count: emails.length,
        logs_created: emailLogs.length
      });
      throw error;
    }
  },

  /**
   * Send ticket notification to all organization members
   * Queries organization users and sends personalized notifications
   */
  async sendOrganizationTicketNotification({
    organizationId,
    ticketData,
    excludeUserIds = []
  }) {
    try {
      if (!organizationId) {
        throw new Error('Organization ID is required for organization-wide notifications');
      }

      // Get organization with users and email integration settings
      const organization = await strapi.entityService.findOne('api::organization.organization', organizationId, {
        populate: {
          users: {
            populate: ['role']
          }
        }
      });

      if (!organization || !organization.users || organization.users.length === 0) {
        strapi.log.warn('⚠️ No users found in organization for ticket notification', { organizationId });
        return { success: true, notifications_sent: 0, message: 'No users to notify' };
      }

      // Filter users based on notification preferences and roles
      const eligibleUsers = organization.users.filter(user => {
        // Exclude specified users (e.g., the ticket creator)
        if (excludeUserIds.includes(user.id)) return false;

        // Only notify confirmed users
        if (!user.confirmed) return false;

        // Only notify users with email addresses
        if (!user.email) return false;

        // TODO: Add notification preference filtering here
        // if (user.notification_preferences && !user.notification_preferences.ticket_notifications) return false;

        return true;
      });

      if (eligibleUsers.length === 0) {
        strapi.log.info('📧 No eligible users for ticket notification', { organizationId, totalUsers: organization.users.length });
        return { success: true, notifications_sent: 0, message: 'No eligible users to notify' };
      }

      // Prepare notification data
      const notificationData = {
        ...ticketData,
        organization_name: organization.name,
        organizationId: organizationId
      };

      // Send notifications to all eligible users
      const emailPromises = eligibleUsers.map(async (user) => {
        try {
          await this.sendTicketNotificationEmail({
            emails: [user.email],
            name: user.username || user.email.split('@')[0],
            ...notificationData,
            userId: user.id
          });
          return { success: true, email: user.email, userId: user.id };
        } catch (error) {
          strapi.log.error('❌ Failed to send ticket notification to user', {
            userId: user.id,
            email: user.email,
            error: error.message
          });
          return { success: false, email: user.email, userId: user.id, error: error.message };
        }
      });

      const results = await Promise.all(emailPromises);
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      strapi.log.info('✅ Organization ticket notifications completed', {
        organizationId,
        organization_name: organization.name,
        total_users: eligibleUsers.length,
        successful_notifications: successCount,
        failed_notifications: failureCount,
        ticket_title: ticketData.title,
        ticket_id: ticketData.ticketId
      });

      return {
        success: true,
        notifications_sent: successCount,
        notifications_failed: failureCount,
        total_eligible_users: eligibleUsers.length,
        results
      };

    } catch (error) {
      strapi.log.error('❌ Organization ticket notification failed', {
        organizationId,
        error: error.message,
        ticket_title: ticketData?.title
      });
      throw error;
    }
  },

  // Utility functions
  loadTemplate,
  clearTemplateCache
};
