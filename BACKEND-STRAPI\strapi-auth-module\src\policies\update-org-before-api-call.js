'use strict';

/**
 * Update organization policy
 */
const axios = require('axios');
const { hasActiveSubscriptionPlan } = require('../helpers/has-subscription');
module.exports = async (policyContext, config, { strapi }) => {
	// Check if this is a password reset flow - allow unauthenticated access
	const isPasswordResetFlow = policyContext.request.url.includes('/auth/reset-password') ||
	                           policyContext.request.url.includes('/reset-password') ||
	                           policyContext.request.headers.referer?.includes('/reset-password');

	if (isPasswordResetFlow) {
		console.log('🔓 Password reset flow detected in update-org policy - skipping org update');
		return true;
	}

	if (policyContext.state.user) {
		// Check subscription first (before external API call)
		if(!policyContext.state.user.organization.current_month_usage){
			const customError = new Error("You don't have subscription");
			customError.status = 403;
			throw customError;
		}

		const isActive = hasActiveSubscriptionPlan(policyContext.state.user.organization);
		if(!isActive){
			let model = await strapi.query('api::organization.organization').update({
				where: {
					id:policyContext.state.user.organization.id,
				},
				data: {
					subscription: 'renewFail'
				}
			});
			const customError = new Error("You don't have subscription");
			customError.status = 403;
			throw customError;
		}

		// Try to update usage from external API (with error handling)
		try {
			const result = await axios.get(
				`${process.env.TALKBASE_BASE_URL}/v4/org?org_id=${policyContext.state.user.organization.org_id}`,
				{
					headers: {
						'Content-Type': 'multipart/form-data'
					},
					timeout: 10000 // 10 second timeout
				}
			);

			// Only update if we got valid data
			if (result && result.data) {
				let model = await strapi.query('api::monthly-usage.monthly-usage').update({
					where: {
						id: policyContext.state.user.organization.current_month_usage.id,
					},
					data: {
						cost_used: result.data.cost || 0,
						credits_used: result.data.credits_used || 0,
						query_count: result.data.query_messages_count || 0,
						query_tokens_count: result.data.query_tokens_count || 0,
						training_tokens_count: result.data.training_tokens_count || 0,
						training_char_count: result.data.training_chars_count || 0,
						cost_query: result.data.cost_query || 0,
						cost_training: result.data.cost_training || 0,
					},
				});
				policyContext.state.user.organization.current_month_usage = model;
				console.log('✅ Organization usage updated from external API');
			} else {
				console.warn('⚠️ External API returned invalid data, skipping usage update');
			}
		} catch (apiError) {
			// Log the error but don't block the request
			console.warn('⚠️ Failed to update organization usage from external API:', {
				error: apiError.message,
				orgId: policyContext.state.user.organization.org_id,
				url: policyContext.request.url
			});

			// For critical operations, you might want to throw an error
			// For now, we'll allow the request to proceed
			console.log('🔄 Proceeding with request despite external API failure');
		}
	}else{
		const customError = new Error("Doesn't have user info");
		customError.status = 500; // Set the desired status code
		throw customError;
	}	
  
	return true; // If you return nothing, Strapi considers you didn't want to block the request and will let it pass

};