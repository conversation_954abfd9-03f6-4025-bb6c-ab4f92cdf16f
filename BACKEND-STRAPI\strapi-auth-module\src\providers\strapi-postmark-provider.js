const postmark = require('postmark');
const https = require('https');

/**
 * Enhanced Strapi Postmark Email Provider
 * Custom provider for Strapi to use Postmark with SSL configuration options
 * Based on the working implementation from NEW EMAIL directory
 */
module.exports = {
  init: (providerOptions = {}, settings = {}) => {
    // SSL/TLS configuration options
    const sslOptions = {
      // Allow disabling SSL verification for development (with warning)
      rejectUnauthorized: process.env.POSTMARK_SSL_VERIFY !== 'false',
      // Custom timeout settings
      timeout: parseInt(process.env.POSTMARK_TIMEOUT) || 30000,
      // Additional SSL options to handle certificate issues
      checkServerIdentity: process.env.POSTMARK_SSL_VERIFY === 'false' ? () => undefined : undefined,
      // Keep connections alive
      keepAlive: true,
      keepAliveMsecs: 1000,
      maxSockets: 50,
      maxFreeSockets: 10,
    };

    // Log SSL configuration for debugging
    if (process.env.POSTMARK_SSL_VERIFY === 'false') {
      console.warn('⚠️ WARNING: Postmark SSL verification is disabled. This should only be used for development!');
    }

    console.log('🔧 Postmark SSL Configuration:', {
      rejectUnauthorized: sslOptions.rejectUnauthorized,
      timeout: sslOptions.timeout,
      environment: process.env.NODE_ENV
    });

    // Create HTTPS agent with custom SSL options
    const httpsAgent = new https.Agent(sslOptions);

    // Initialize Postmark client with enhanced configuration
    const client = new postmark.ServerClient(providerOptions.apiKey, {
      // Pass custom request options if the client supports it
      requestOptions: {
        agent: httpsAgent,
        timeout: sslOptions.timeout,
      }
    });

    // Test connection on initialization to catch SSL issues early
    const testConnection = async () => {
      try {
        console.log('🔍 Testing Postmark connection...');
        await client.getServer();
        console.log('✅ Postmark connection test successful');
        return true;
      } catch (error) {
        console.error('❌ Postmark connection test failed:', error.message);

        // Provide specific guidance for SSL issues
        if (error.message.includes('certificate') || error.message.includes('SSL') || error.message.includes('TLS') || error.message.includes('foxoms.com')) {
          console.error('🔒 SSL Certificate Error Detected:');
          console.error('   This appears to be a network-level SSL interception issue.');
          console.error('   Possible causes:');
          console.error('   - Corporate firewall/proxy doing SSL inspection');
          console.error('   - DNS hijacking or incorrect DNS resolution');
          console.error('   - Docker network configuration issues');
          console.error('   Solutions:');
          console.error('   - Set POSTMARK_SSL_VERIFY=false for development (temporary)');
          console.error('   - Check network/proxy configuration');
          console.error('   - Verify DNS resolution for api.postmarkapp.com');
        }

        return false;
      }
    };

    // Run connection test in background (don't block initialization)
    if (process.env.NODE_ENV !== 'test') {
      setTimeout(testConnection, 1000);
    }

    return {
      send: async (options) => {
        const { from, to, cc, bcc, replyTo, subject, text, html, ...rest } = options;

        // Enhanced logging for debugging
        console.log('📧 Attempting to send email via Postmark:', {
          to: Array.isArray(to) ? to : [to],
          from: from || settings.defaultFrom,
          subject,
          sslVerify: process.env.POSTMARK_SSL_VERIFY !== 'false'
        });

        const message = {
          From: from || settings.defaultFrom,
          To: Array.isArray(to) ? to.join(',') : to,
          Subject: subject,
          ...(html && { HtmlBody: html }),
          ...(text && { TextBody: text }),
          ...(replyTo && { ReplyTo: replyTo }),
          ...(cc && { Cc: Array.isArray(cc) ? cc.join(',') : cc }),
          ...(bcc && { Bcc: Array.isArray(bcc) ? bcc.join(',') : bcc }),
        };

        try {
          const result = await client.sendEmail(message);

          console.log('✅ Postmark email sent successfully:', {
            messageId: result.MessageID,
            to: result.To,
            subject: result.Subject || subject
          });

          return {
            messageId: result.MessageID,
            response: result,
          };
        } catch (error) {
          console.error('❌ Postmark email send failed:', {
            error: error.message,
            code: error.code,
            statusCode: error.statusCode,
            to: Array.isArray(to) ? to : [to],
            from: from || settings.defaultFrom,
            subject
          });

          // Enhanced error handling for SSL issues
          if (error.message.includes('certificate') || error.message.includes('SSL') || error.message.includes('TLS')) {
            console.error('🔒 SSL/TLS Error detected. Consider setting POSTMARK_SSL_VERIFY=false for development or check network configuration.');
          }

          throw new Error(`Failed to send email via Postmark: ${error.message}`);
        }
      },
    };
  },
};
