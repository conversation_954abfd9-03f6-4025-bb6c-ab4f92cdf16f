import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import axios from 'axios';
import history from '@history';

function EmailConfirmation() {
  const location = useLocation();
  const [confirmationStatus, setConfirmationStatus] = useState('processing'); // 'processing', 'success', 'error'
  const [message, setMessage] = useState('Processing your email confirmation...');

  useEffect(() => {
    const processEmailConfirmation = async () => {
      try {
        // Extract confirmation token from URL
        const urlParams = new URLSearchParams(location.search);
        const confirmationToken = urlParams.get('confirmation');

        if (!confirmationToken) {
          setConfirmationStatus('error');
          setMessage('Invalid confirmation link. Please check your email and try again.');
          return;
        }

        console.log('📧 Processing email confirmation with token:', confirmationToken);

        // Call backend to confirm email
        const response = await axios.get(
          `${process.env.REACT_APP_AUTH_BASE_URL}/api/auth/email-confirmation?confirmation=${confirmationToken}`
        );

        if (response.data && response.data.confirmed) {
          setConfirmationStatus('success');
          setMessage('Email confirmed successfully! Welcome to Ajentic.');

          // Auto-login if JWT token is provided
          if (response.data.jwt) {
            localStorage.setItem('jwt_access_token', response.data.jwt);
            console.log('✅ Auto-login after email confirmation');
          }
        } else {
          setConfirmationStatus('error');
          setMessage('Email confirmation failed. Please try again or contact support.');
        }

      } catch (error) {
        console.error('❌ Email confirmation error:', error);
        setConfirmationStatus('error');

        if (error.response?.status === 400) {
          setMessage(error.response.data.message || 'Invalid or expired confirmation link.');
        } else {
          setMessage('Email confirmation failed. Please try again or contact support.');
        }
      }
    };

    processEmailConfirmation();
  }, [location]);

  useEffect(() => {
    // Redirect to sign-in after delay, regardless of status
    const delay = confirmationStatus === 'success' ? 3000 : 5000;
    const timer = setTimeout(() => {
      history.push('/sign-in');
    }, delay);

    return () => clearTimeout(timer);
  }, [confirmationStatus]);

  return (
    <div className="flex flex-col flex-auto items-center sm:justify-center min-w-0 bg-[#281463]">
      <Paper className="flex  shadow-md shadow-base-purple items-center rounded-md w-full sm:w-auto min-h-full sm:min-h-auto rounded-0 py-32 px-16 sm:p-48">
        <div className="w-full max-w-320 sm:w-320 mx-auto sm:mx-0">
          <div className="flex w-full items-center align-center justify-center mb-24">
            <img className="logo-icon w-28" src="assets/images/logo/ajentic-logo.png" alt="logo" />
            <span className="logo-heading">Ajentic</span>
          </div>
          <Typography className={`mt-32 text-xl font-bold tracking-tight leading-tight text-center ${
            confirmationStatus === 'success' ? 'text-green-600' :
            confirmationStatus === 'error' ? 'text-red-600' : 'text-blue-600'
          }`}>
            {confirmationStatus === 'processing' && '⏳ Processing Email Confirmation'}
            {confirmationStatus === 'success' && '✅ Email Confirmed Successfully!'}
            {confirmationStatus === 'error' && '❌ Email Confirmation Failed'}
          </Typography>
          <Typography className="mt-32 text-base font-regular tracking-tight leading-tight text-center">
            {message}
          </Typography>
          <Typography className="mt-16 text-sm text-gray-600 text-center">
              You will be redirected to login page soon
          </Typography>
        </div>
      </Paper>
    </div>
  );
}

export default EmailConfirmation;
